"""
MADDPG Resource Allocator for Multi-Task Federated Learning

This module provides a MADDPG-based resource allocation system that replaces MAPPO
for multi-task federated learning scenarios.
"""

import numpy as np
import torch
import logging
from typing import Dict, List, Any
from entity.entitys import ResourceAllocation
from maddpg.maddpg import MADDPG

logger = logging.getLogger(__name__)


class MADDPGResourceAllocator:
    """
    MADDPG-based resource allocator for multi-task federated learning
    
    Each client is treated as an agent that needs to make decisions about:
    - Task selection (select_ij)
    - Computing resource allocation (q_ij) 
    - Bandwidth allocation (B_ij)
    - Power allocation (P_ij)
    - Task priority (Task_ij)
    """
    
    def __init__(self, num_clients: int, num_tasks: int, device: str = 'cpu'):
        self.num_clients = num_clients
        self.num_tasks = num_tasks
        self.device = device
        
        # State dimension: [client_capability, task_requirements, network_conditions, historical_performance]
        # For each client: [compute_capability, bandwidth_capability, power_capability] + 
        #                  [task_0_req, task_1_req, task_2_req] +
        #                  [network_latency, network_throughput] +
        #                  [last_round_performance]
        self.state_dim = 3 + num_tasks + 2 + 1  # = 9 for 3 tasks
        
        # Action dimension: [select_ij, q_ij, B_ij, P_ij, Task_ij] for each task
        self.action_dim = 5 * num_tasks  # = 15 for 3 tasks
        
        # Initialize MADDPG
        self.maddpg = MADDPG(
            device=device,
            num_agents=num_clients,
            state_dim=self.state_dim,
            action_dim=self.action_dim
        )
        
        # Experience storage
        self.last_states = None
        self.last_actions = None
        
        logger.info(f"MADDPG Resource Allocator initialized:")
        logger.info(f"  Clients: {num_clients}, Tasks: {num_tasks}")
        logger.info(f"  State dim: {self.state_dim}, Action dim: {self.action_dim}")
        logger.info(f"  Device: {device}")

    def get_resource_allocations(self, client_states: List[Dict[str, Any]]) -> Dict[int, ResourceAllocation]:
        """
        Get resource allocations for all clients using MADDPG
        
        Args:
            client_states: List of client state dictionaries
            
        Returns:
            Dict mapping client_id to ResourceAllocation
        """
        # Convert client states to MADDPG format
        maddpg_states = self._convert_states_to_maddpg_format(client_states)
        
        # Get actions from MADDPG
        actions = self.maddpg.get_actions(maddpg_states, add_noise=True)
        
        # Convert actions to ResourceAllocation format
        resource_allocations = self._convert_actions_to_resource_allocations(actions)
        
        # Store for experience replay
        self.last_states = maddpg_states
        self.last_actions = actions
        
        logger.debug(f"Generated resource allocations for {len(resource_allocations)} clients")
        return resource_allocations

    def update_with_rewards(self, rewards: Dict[int, float], next_client_states: List[Dict[str, Any]]):
        """
        Update MADDPG with rewards and next states
        
        Args:
            rewards: Dict mapping client_id to reward
            next_client_states: List of next client state dictionaries
        """
        if self.last_states is None or self.last_actions is None:
            logger.warning("No previous states/actions to update with")
            return
        
        # Convert next states
        next_maddpg_states = self._convert_states_to_maddpg_format(next_client_states)
        
        # Convert rewards to list format
        reward_list = [rewards.get(i, 0.0) for i in range(self.num_clients)]
        
        # Add experience to replay buffer
        self.maddpg.add_experience(
            states=self.last_states,
            actions=self.last_actions,
            rewards=reward_list,
            next_states=next_maddpg_states
        )
        
        # Update networks if we have enough experiences
        batch_size = 64
        if self.maddpg.can_update(batch_size):
            self.maddpg.update(batch_size)
            logger.debug("MADDPG networks updated")

    def _convert_states_to_maddpg_format(self, client_states: List[Dict[str, Any]]) -> List[np.ndarray]:
        """Convert client states to MADDPG input format"""
        maddpg_states = []
        
        for i, state in enumerate(client_states):
            # Extract state features
            compute_capability = state.get('compute_capability', 0.5)
            bandwidth_capability = state.get('bandwidth_capability', 0.5)
            power_capability = state.get('power_capability', 0.5)
            
            # Task requirements (normalized)
            task_requirements = []
            for j in range(self.num_tasks):
                task_req = state.get(f'task_{j}_requirement', 0.5)
                task_requirements.append(task_req)
            
            # Network conditions
            network_latency = state.get('network_latency', 50.0) / 100.0  # Normalize to [0,1]
            network_throughput = state.get('network_throughput', 100.0) / 200.0  # Normalize to [0,1]
            
            # Historical performance
            last_performance = state.get('last_round_performance', 0.5)
            
            # Combine all features
            state_vector = np.array([
                compute_capability,
                bandwidth_capability, 
                power_capability,
                *task_requirements,
                network_latency,
                network_throughput,
                last_performance
            ], dtype=np.float32)
            
            maddpg_states.append(state_vector)
        
        return maddpg_states

    def _convert_actions_to_resource_allocations(self, actions: List[np.ndarray]) -> Dict[int, ResourceAllocation]:
        """Convert MADDPG actions to ResourceAllocation format"""
        resource_allocations = {}
        
        for client_id, action in enumerate(actions):
            # Split action into components for each task
            select_ij = {}
            q_ij = {}
            B_ij = {}
            P_ij = {}
            Task_ij = {}
            
            for task_id in range(self.num_tasks):
                task_name = f"task_{task_id}"
                base_idx = task_id * 5
                
                # Extract action components (all in [0,1] range due to sigmoid)
                select_ij[task_name] = float(action[base_idx])
                q_ij[task_name] = float(action[base_idx + 1])
                B_ij[task_name] = float(action[base_idx + 2])
                P_ij[task_name] = float(action[base_idx + 3])
                Task_ij[task_name] = float(action[base_idx + 4])
            
            resource_allocations[client_id] = ResourceAllocation(
                client_id=client_id,
                select_ij=select_ij,
                q_ij=q_ij,
                B_ij=B_ij,
                P_ij=P_ij,
                Task_ij=Task_ij
            )
        
        return resource_allocations

    def save_model(self, path: str):
        """Save MADDPG model"""
        self.maddpg.save_checkpoint(path)
        logger.info(f"MADDPG model saved to {path}")

    def load_model(self, path: str):
        """Load MADDPG model"""
        self.maddpg.load_checkpoint(path)
        logger.info(f"MADDPG model loaded from {path}")

    def get_training_stats(self) -> Dict[str, Any]:
        """Get training statistics"""
        return {
            'replay_buffer_size': len(self.maddpg.replay_buffer.buffer),
            'num_agents': self.num_clients,
            'state_dim': self.state_dim,
            'action_dim': self.action_dim
        }
