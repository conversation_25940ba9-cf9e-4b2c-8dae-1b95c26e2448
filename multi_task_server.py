"""
Multi-Task Federated Learning Server

This module extends the existing federated learning server to handle multiple tasks
with MADDPG resource allocation and NS3 network simulation integration.
"""

import logging
import sys
import numpy as np
import time
from typing import Dict, List, Any

# Import our multi-task components
from multi_task_client import MultiTaskClient
from entity.entitys import ResourceAllocation, NetworkMetrics, TaskConfig, TaskType, MultiTaskFLConfig
from maddpg_allocator import MADDPGResourceAllocator
from ns3_socket.network_ns3 import NS3MultiTaskInterface


class MultiTaskFederatedServer:
    """
    Multi-task federated learning server with MADDPG resource allocation
    and NS3 network simulation integration
    """

    def __init__(self, config):
        # Initialize base server (but skip automatic boot)
        self.config = config

        # Multi-task specific configuration
        self.mt_config = MultiTaskFLConfig()
        if hasattr(config, 'multi_task'):
            self.mt_config = config.multi_task

        # Default task configurations
        if self.mt_config.tasks is None:
            self.mt_config.tasks = {
                "mnist": TaskConfig("mnist", TaskType.IMAGE_CLASSIFICATION, "data/MNIST", "cnn"),
                "cifar10": TaskConfig("cifar10", TaskType.IMAGE_CLASSIFICATION, "data/cifar-10-batches-py", "resnet"),
                "airquality": TaskConfig("airquality", TaskType.TIME_SERIES, "data/air_quality", "gru")
            }

        # MADDPG resource allocator
        self.maddpg_allocator = MADDPGResourceAllocator(
            num_clients=self.mt_config.num_clients,
            num_tasks=len(self.mt_config.tasks),
            device='cpu'  # Can be changed to 'cuda' if GPU is available
        )

        # Initialize clients with multi-task configurations
        self.clients = []
        for i in range(self.mt_config.num_clients):
            client = MultiTaskClient(
                client_id=i,
                task_configs=list(self.mt_config.tasks.values())
            )
            self.clients.append(client)

        self.logger = logging.getLogger(__name__)

        # Network simulation
        self.network = None
        self.network_connected = False

        # Training state
        self.current_round = 0
        self.round_metrics: List[Dict[str, Any]] = []

        self.logger = logging.getLogger(__name__)
        self.logger.info("Multi-Task Federated Server initialized")

    def make_clients(self, num_clients):
        """Create multi-task clients"""
        self.logger.info(f'Creating {num_clients} multi-task clients...')

        # Create multi-task clients
        clients = []
        for client_id in range(num_clients):
            # Create multi-task client
            client = MultiTaskClient(client_id, self.mt_config.tasks)

            # Set similarity threshold
            client.similarity_threshold = self.mt_config.similarity_threshold

            # Analyze task similarity (will be cached after first computation)
            client.analyze_task_similarity()

            # Set link properties (compatibility with existing FL framework)
            if hasattr(self, 'config'):
                client.set_link(self.config)

            clients.append(client)

        self.clients = clients
        self.logger.info(f'Created {len(clients)} multi-task clients')

    def setup_network_simulation(self):
        """Setup NS3 network simulation"""
        try:
            self.logger.info("Setting up NS3 network simulation...")
            self.network = NS3MultiTaskInterface(config=self.config)

            # Connect to NS3 simulation
            if self.network.connect():
                self.network_connected = True
                self.logger.info("NS3 network simulation connected successfully")
            else:
                self.network_connected = False
                self.logger.warning("Failed to connect to NS3 simulation")

        except Exception as e:
            self.logger.warning(f"Failed to setup NS3 network: {e}")
            self.logger.warning("Continuing without network simulation")
            self.network_connected = False

    def run_federated_round(self, round_num: int) -> Dict[str, Any]:
        """Run a single federated learning round with MADDPG and NS3 integration

        Workflow:
        1. MADDPG资源分配 → 2. NS3仿真传输 → 3. 客户端训练 → 4. 服务器聚合 → 5. MADDPG奖励反馈
        """
        self.logger.info(f"Starting federated round {round_num}")
        round_start_time = time.time()

        # 1. Get states from all available clients for MAPPO decision making
        available_clients = self._get_available_clients()
        self.logger.info(f"Available clients for this round: {[c.client_id for c in available_clients]}")

        # 2. Check task similarity for all available clients (cached after first computation)
        self._ensure_task_similarity(available_clients)

        # 3. Get current states from all available clients for MADDPG
        client_states = self._collect_client_states(available_clients)

        # 4. MADDPG resource allocation (包括客户端选择 + 计算资源 + 无线功率分配)
        resource_allocations = self.maddpg_allocator.get_resource_allocations(
            client_states)
        self.logger.info("Step 1: MADDPG resource allocation completed")

        # 5. Select clients based on MADDPG decisions (select_ij values)
        selected_clients = self._select_clients_by_maddpg(available_clients, resource_allocations)
        self.logger.info(
            f"MADDPG selected {len(selected_clients)} clients: {[c.client_id for c in selected_clients]}")

        # 6. Apply resource allocations to selected clients
        for client in selected_clients:
            if client.client_id in resource_allocations:
                client.set_resource_allocation(
                    resource_allocations[client.client_id])

        # 6. NS3 network simulation - 仿真模型传输过程
        self.logger.info(
            "Step 2: Starting NS3 network simulation for model transmission...")
        network_metrics = self._simulate_model_transmission_with_ns3(
            resource_allocations, len(self.mt_config.tasks))

        # 7. Apply network metrics to clients (传输延迟等影响训练)
        for client in selected_clients:
            if client.client_id in network_metrics:
                client.set_network_metrics(network_metrics[client.client_id])

        # 8. Run client training (基于资源分配和网络约束)
        self.logger.info(
            "Step 3: Starting client training with allocated resources...")
        training_reports = self._run_client_training(selected_clients)

        # 9. Aggregate models (服务器聚合)
        self.logger.info("Step 4: Aggregating models at server...")
        aggregated_weights = self._aggregate_models(training_reports)

        # 10. Calculate rewards for MADDPG (基于性能和效率)
        rewards = self._calculate_maddpg_rewards(
            selected_clients, training_reports, network_metrics)

        # 11. Update MADDPG with rewards (强化学习反馈)
        self.logger.info(
            "Step 5: Updating MADDPG with performance rewards...")
        next_states = self._collect_client_states(selected_clients)
        self.maddpg_allocator.update_with_rewards(rewards, next_states)

        # 12. Store round metrics
        round_metrics = self._collect_round_metrics(
            round_num, selected_clients, training_reports,
            resource_allocations, network_metrics, rewards
        )
        self.round_metrics.append(round_metrics)

        round_duration = time.time() - round_start_time
        self.logger.info(
            f"Round {round_num} completed in {round_duration:.2f}s")
        self.logger.info(
            f"Workflow: MADDPG → NS3 → Training → Aggregation → Rewards")

        return round_metrics

    def _ensure_task_similarity(self, clients: List[MultiTaskClient]):
        """Ensure task similarity is computed for all clients (cached after first computation)"""
        for client in clients:
            if not client.similarity_computed:
                self.logger.info(
                    f"Computing task similarity for client {client.client_id} (first time)")
                client.analyze_task_similarity()
            else:
                self.logger.debug(
                    f"Client {client.client_id} using cached task similarity")

    def _get_available_clients(self) -> List[MultiTaskClient]:
        """Get all available clients for MADDPG decision making"""
        # Return all clients - MADDPG will decide which ones to select
        return self.clients

    def _select_clients_by_maddpg(self, available_clients: List[MultiTaskClient],
                                 resource_allocations: Dict[int, ResourceAllocation]) -> List[MultiTaskClient]:
        """
        Select clients based on MADDPG decisions (select_ij values)

        如果客户端的allocation.select_ij中有任何一个任务j>0，就说明该客户端参与训练
        """
        selected_clients = []

        for client in available_clients:
            if client.client_id in resource_allocations:
                allocation = resource_allocations[client.client_id]

                # 检查是否有任何任务的select_ij值大于0
                has_selected_task = False
                selected_tasks = []

                for task_name, select_value in allocation.select_ij.items():
                    if select_value >= 0.5:
                        has_selected_task = True
                        selected_tasks.append(f"{task_name}({select_value:.3f})")

                if has_selected_task:
                    selected_clients.append(client)
                    self.logger.debug(f"Client {client.client_id} selected for tasks: {', '.join(selected_tasks)}")
                else:
                    self.logger.debug(f"Client {client.client_id} not selected - no tasks with select_ij > 0")
            else:
                self.logger.warning(f"Client {client.client_id} has no resource allocation")

        # 确保至少有一个客户端被选中（回退机制）
        if not selected_clients and available_clients:
            # 选择具有最高总选择概率的客户端
            def get_selection_score(client):
                if client.client_id in resource_allocations:
                    allocation = resource_allocations[client.client_id]
                    return sum(allocation.select_ij.values())
                return 0

            best_client = max(available_clients, key=get_selection_score)
            selected_clients.append(best_client)

            # 记录回退选择的详细信息
            if best_client.client_id in resource_allocations:
                allocation = resource_allocations[best_client.client_id]
                task_scores = [f"{task}({score:.3f})" for task, score in allocation.select_ij.items()]
                self.logger.info(f"Fallback: Selected client {best_client.client_id} with task scores: {', '.join(task_scores)}")
            else:
                self.logger.info(f"Fallback: Selected client {best_client.client_id} (no allocation available)")

        self.logger.info(f"MADDPG client selection completed: {len(selected_clients)}/{len(available_clients)} clients selected")
        return selected_clients

    def _collect_client_states(self, clients: List[MultiTaskClient]) -> List[Dict[str, Any]]:
        """Collect current states from clients for MADDPG"""
        states = []
        for client in clients:
            state = client.get_state_for_maddpg()
            states.append(state)
        return states

    def _simulate_model_transmission_with_ns3(self, client_allocations: Dict[int, ResourceAllocation], num_tasks: int):
        """
        使用NS3仿真模型传输过程 - 这是核心的网络仿真步骤

        在客户端训练前，仿真模型从服务器到客户端的传输过程，
        考虑MAPPO分配的无线功率和网络条件
        """
        self.logger.info(f"Running simulation for {len(client_allocations)} clients, {num_tasks} tasks")

        self.network.send_simulation_command(client_allocations, len(client_allocations), num_tasks)
        
        results = self.network.receive_results()
    
        self.logger.info(f"Simulation completed, received task results")
        return results

    def _run_client_training(self, clients: List[MultiTaskClient]) -> List[Any]:
        """
        执行真正的联邦学习客户端训练流程

        流程：
        1. 模型分发：将全局模型发送给客户端
        2. 本地训练：客户端基于资源分配和网络约束进行多任务训练
        3. 模型上传：收集训练后的模型参数
        """
        self.logger.info("Starting federated client training...")
        self.logger.info(f"Training {len(clients)} clients with allocated resources and network constraints")

        reports = []

        # Step 1: 模型分发 - 将当前全局模型发送给所有客户端
        self.logger.info("Step 1: Distributing global model to clients...")
        self._distribute_global_model(clients)

        # Step 2: 并行本地训练 - 每个客户端基于分配的资源进行多任务训练
        self.logger.info("Step 2: Running parallel local training on clients...")
        for client in clients:
            try:
                # 设置训练环境
                client.current_round = self.current_round

                # 应用网络约束（传输延迟影响训练开始时间）
                if client.network_metrics:
                    transmission_delay = client.network_metrics.execution_time
                    self.logger.info(f"Client {client.client_id}: Applying transmission delay {transmission_delay:.2f}s")
                    time.sleep(min(0.1, transmission_delay * 0.01))  # 模拟延迟（缩放）

                # 执行多任务本地训练
                self.logger.info(f"Client {client.client_id}: Starting multi-task local training...")
                training_start_time = time.time()

                # 真正的多任务训练
                local_training_metrics = self._execute_local_multi_task_training(client)

                training_duration = time.time() - training_start_time

                # 创建训练报告
                report = self._create_training_report(client, local_training_metrics, training_duration)
                reports.append(report)

                self.logger.info(f"Client {client.client_id}: Local training completed in {training_duration:.2f}s")
                self.logger.info(f"  Tasks trained: {list(local_training_metrics.keys())}")
                total_power = sum(client.current_allocation.P_ij.values()) if client.current_allocation else 0
                self.logger.info(f"  Resource utilization: CPU={self._get_cpu_utilization(client):.2f}, "
                               f"Power={total_power:.2f}")

            except Exception as e:
                self.logger.error(f"Client {client.client_id} training failed: {e}")
                # 创建失败报告
                failed_report = self._create_failed_training_report(client, str(e))
                reports.append(failed_report)

        # Step 3: 收集训练结果
        self.logger.info("Step 3: Collecting training results from clients...")
        successful_clients = len([r for r in reports if hasattr(r, 'success') and r.success])
        self.logger.info(f"Training completed: {successful_clients}/{len(clients)} clients successful")

        return reports

    def _distribute_global_model(self, clients: List[MultiTaskClient]):
        """将全局模型分发给客户端"""
        self.logger.info("Distributing global model to clients...")

        for client in clients:
            try:
                # 模拟模型分发过程
                if hasattr(self, 'global_model_weights') and self.global_model_weights:
                    # 将全局模型权重发送给客户端
                    client.receive_global_model(self.global_model_weights)
                    self.logger.debug(f"Global model distributed to client {client.client_id}")
                else:
                    # 第一轮，客户端使用初始模型
                    self.logger.debug(f"Client {client.client_id} using initial model (first round)")

            except Exception as e:
                self.logger.error(f"Failed to distribute model to client {client.client_id}: {e}")

    def _execute_local_multi_task_training(self, client: MultiTaskClient) -> Dict[str, Dict[str, float]]:
        """执行客户端的多任务本地训练"""
        # 基于任务相似性和资源分配执行训练
        training_metrics = {}

        # 确保任务相似性已计算
        if not client.similarity_computed:
            client.analyze_task_similarity()

        # 根据资源分配调整训练参数
        if client.current_allocation:
            # 基于计算资源分配调整批次大小和学习率
            total_compute = sum(client.current_allocation.q_ij.values())
            batch_size_factor = min(2.0, max(0.5, total_compute))
            learning_rate_factor = min(1.5, max(0.5, total_compute))

            self.logger.debug(f"Client {client.client_id}: Adjusted training params based on compute allocation")
            self.logger.debug(f"  Total Compute: {total_compute:.2f}, Batch factor: {batch_size_factor:.2f}")

        # 执行实际的多任务训练
        try:
            # 调用客户端的多任务训练方法
            training_metrics = client.run()

            # 添加资源利用率信息
            if client.current_allocation:
                for task_name in training_metrics:
                    if isinstance(training_metrics[task_name], dict):
                        training_metrics[task_name]['compute_utilization'] = client.current_allocation.q_ij.get(task_name, 0.0)
                        training_metrics[task_name]['power_allocation'] = client.current_allocation.P_ij.get(task_name, 0.0)
                        training_metrics[task_name]['bandwidth_allocation'] = client.current_allocation.B_ij.get(task_name, 0.0)
                        training_metrics[task_name]['selection_probability'] = client.current_allocation.select_ij.get(task_name, 0.0)
                        training_metrics[task_name]['task_priority'] = client.current_allocation.Task_ij.get(task_name, 0.0)

        except Exception as e:
            self.logger.error(f"Multi-task training failed for client {client.client_id}: {e}")
            # 返回默认指标
            training_metrics = {task: {'loss': 10.0, 'accuracy': 0.0} for task in client.task_configs.keys()}

        return training_metrics

    def _create_training_report(self, client: MultiTaskClient, training_metrics: Dict[str, Dict[str, float]],
                              training_duration: float):
        """创建训练报告"""
        # 计算平均性能指标
        total_loss = 0.0
        total_accuracy = 0.0
        num_tasks = len(training_metrics)

        for task_metrics in training_metrics.values():
            if isinstance(task_metrics, dict):
                total_loss += task_metrics.get('loss', 0.0)
                total_accuracy += task_metrics.get('accuracy', 0.0)

        avg_loss = total_loss / max(1, num_tasks)
        avg_accuracy = total_accuracy / max(1, num_tasks)

        # 获取模型权重（简化）
        model_weights = None
        try:
            model_weights = client.get_model_weights()
        except:
            model_weights = {}

        # 计算网络延迟
        network_delay = 0.0
        if client.network_metrics:
            network_delay = client.network_metrics.execution_time

        # 创建报告对象（兼容原有FL框架的Report格式）
        report = type('TrainingReport', (), {
            'client_id': client.client_id,
            'success': True,
            'loss': avg_loss,
            'accuracy': avg_accuracy,
            'training_time': training_duration,
            'model_weights': model_weights,
            'task_metrics': training_metrics,
            'num_tasks': num_tasks,
            'shared_tasks': len(client.shared_tasks),
            'independent_tasks': len(client.independent_tasks),
            'resource_allocation': client.current_allocation,
            'network_metrics': client.network_metrics,
            'delay': network_delay,  # 添加delay属性以兼容原有框架
            'weights': [(f'task_{i}', model_weights) for i, model_weights in enumerate([model_weights])]  # 兼容原有格式
        })()

        return report

    def _create_failed_training_report(self, client: MultiTaskClient, error_msg: str):
        """创建失败的训练报告"""
        # 计算网络延迟
        network_delay = 0.0
        if client.network_metrics:
            network_delay = client.network_metrics.execution_time

        report = type('FailedTrainingReport', (), {
            'client_id': client.client_id,
            'success': False,
            'loss': 10.0,  # 高损失表示失败
            'accuracy': 0.0,
            'training_time': 0.0,
            'model_weights': None,
            'task_metrics': {},
            'error_message': error_msg,
            'num_tasks': len(client.task_configs),
            'resource_allocation': client.current_allocation,
            'network_metrics': client.network_metrics,
            'delay': network_delay,  # 添加delay属性以兼容原有框架
            'weights': []  # 失败时没有权重
        })()

        return report

    def _get_cpu_utilization(self, client: MultiTaskClient) -> float:
        """获取客户端计算资源利用率"""
        if client.current_allocation and client.current_allocation.q_ij:
            return sum(client.current_allocation.q_ij.values())
        return 0.0

    def _aggregate_models(self, reports: List[Any]) -> Dict[str, Any]:
        """
        聚合客户端模型权重 - 真正的联邦学习聚合过程

        使用FedAvg算法聚合多任务模型，考虑任务相似性
        """
        if not reports:
            self.logger.warning("No training reports to aggregate")
            return {}

        # 过滤成功的训练报告
        successful_reports = [r for r in reports if hasattr(r, 'success') and r.success and hasattr(r, 'model_weights') and r.model_weights]

        if not successful_reports:
            self.logger.warning("No successful training reports with model weights")
            return {}

        self.logger.info(f"Aggregating models from {len(successful_reports)} successful clients")

        # 收集所有任务的权重
        task_weights_collection = {}

        for report in successful_reports:
            client_id = report.client_id

            # 收集每个任务的权重
            for task_name, task_weights in report.model_weights.items():
                if task_name not in task_weights_collection:
                    task_weights_collection[task_name] = []

                task_weights_collection[task_name].append({
                    'client_id': client_id,
                    'weights': task_weights,
                    'performance': 1.0 / (1.0 + report.loss),  # 性能权重
                    'num_samples': 100  # 简化：假设每个客户端有100个样本
                })

        # 执行简化的FedAvg聚合
        aggregated_weights = {}

        for task_name, task_data in task_weights_collection.items():
            self.logger.info(f"Aggregating task '{task_name}' from {len(task_data)} clients")

            # 简化聚合：取第一个客户端的权重作为聚合结果
            # 在实际应用中，这里应该实现真正的权重平均
            if task_data:
                aggregated_weights[task_name] = task_data[0]['weights']
                self.logger.debug(f"Task '{task_name}': Used weights from client {task_data[0]['client_id']}")

        # 保存全局模型权重
        self.global_model_weights = aggregated_weights

        # 记录聚合统计信息
        self.logger.info(f"Model aggregation completed:")
        self.logger.info(f"  Tasks aggregated: {len(aggregated_weights)}")
        self.logger.info(f"  Participating clients: {len(successful_reports)}")

        return aggregated_weights

    def _calculate_maddpg_rewards(self, clients: List[MultiTaskClient], reports: List[Any],
                                  network_metrics: Dict[int, NetworkMetrics]) -> Dict[int, float]:
        """Calculate rewards for MADDPG based on training performance, network efficiency, and resource utilization"""
        rewards = {}

        for i, client in enumerate(clients):
            if i < len(reports):
                report = reports[i]
                client_network_metrics = network_metrics.get(client.client_id)

                # 1. Training Performance Reward (lower loss is better)
                # Scale loss to reward
                performance_reward = max(0, 2.0 - report.loss)

                # 2. Network Efficiency Reward (基于NS3仿真结果)
                if client_network_metrics:
                    # 网络传输效率：更高的吞吐量和更低的延迟获得更高奖励
                    throughput_reward = min(
                        1.0, client_network_metrics.throughput / 1000.0)  # Normalize to [0,1]
                    # Lower latency is better
                    latency_penalty = max(
                        0, 1.0 - client_network_metrics.latency / 100.0)
                    # Faster transmission
                    round_time_reward = max(
                        0, 1.0 - client_network_metrics.execution_time / 5.0)
                    network_reward = (throughput_reward +
                                      latency_penalty + round_time_reward) / 3.0
                else:
                    network_reward = 0.3  # Default if no network metrics

                # 3. Resource Allocation Efficiency Reward
                if client.current_allocation:
                    # 计算资源利用效率
                    total_compute = sum(
                        client.current_allocation.q_ij.values())
                    # Prefer ~80% compute usage
                    compute_efficiency = 1.0 - abs(total_compute - 0.8)

                    # 传输功率效率 (功率与网络性能的平衡)
                    total_power = sum(client.current_allocation.P_ij.values())
                    if client_network_metrics:
                        # 功率效率 = 网络性能 / 功率消耗
                        power_efficiency = (
                            client_network_metrics.throughput / 1000.0) / max(0.1, total_power)
                        power_efficiency = min(
                            1.0, power_efficiency)  # Cap at 1.0
                    else:
                        power_efficiency = 0.5

                    resource_reward = (compute_efficiency + power_efficiency) / 2.0
                    resource_reward = max(0, resource_reward)
                else:
                    resource_reward = 0.0

                # 4. Task Similarity Bonus (智能模型共享)
                similarity_bonus = 0.1 if len(client.shared_tasks) > 0 else 0.0

                # 5. AI-Network Co-optimization Bonus
                # 奖励那些在任务相似性指导下做出好的资源分配决策的客户端
                if len(client.shared_tasks) > 0 and client.current_allocation:
                    # 共享任务的客户端如果合理分配资源可以获得额外奖励
                    shared_task_bonus = 0.05 * len(client.shared_tasks)
                else:
                    shared_task_bonus = 0.0

                # Total reward (weighted combination)
                total_reward = (
                    0.4 * performance_reward +      # 40% 训练性能
                    0.3 * network_reward +          # 30% 网络效率
                    0.2 * resource_reward +         # 20% 资源效率
                    0.1 * (similarity_bonus + shared_task_bonus)  # 10% 协同优化
                )

                rewards[client.client_id] = total_reward

                self.logger.debug(f"Client {client.client_id} reward breakdown: "
                                  f"perf={performance_reward:.3f}, net={network_reward:.3f}, "
                                  f"res={resource_reward:.3f}, sim={similarity_bonus:.3f}, "
                                  f"total={total_reward:.3f}")
            else:
                rewards[client.client_id] = 0.0

        return rewards

    def _collect_round_metrics(self, round_num: int, clients: List[MultiTaskClient],
                               reports: List[Any], allocations: Dict[int, ResourceAllocation],
                               network_metrics: Dict[int, NetworkMetrics],
                               rewards: Dict[int, float]) -> Dict[str, Any]:
        """Collect comprehensive metrics for the round"""

        # Training metrics
        avg_loss = np.mean(
            [report.loss for report in reports]) if reports else 0.0
        avg_accuracy = np.mean(
            [report.accuracy for report in reports]) if reports else 0.0
        avg_delay = np.mean(
            [report.delay for report in reports]) if reports else 0.0

        # Network metrics
        avg_throughput = np.mean(
            [nm.throughput for nm in network_metrics.values()]) if network_metrics else 0.0
        avg_round_time = np.mean(
            [nm.execution_time for nm in network_metrics.values()]) if network_metrics else 0.0

        # MADDPG metrics
        avg_reward = np.mean(list(rewards.values())) if rewards else 0.0
        maddpg_stats = self.maddpg_allocator.get_training_stats()

        # Task similarity metrics
        shared_task_count = sum(len(client.shared_tasks) for client in clients)
        independent_task_count = sum(
            len(client.independent_tasks) for client in clients)

        return {
            'round': round_num,
            'timestamp': time.time(),
            'num_clients': len(clients),
            'training_metrics': {
                'avg_loss': avg_loss,
                'avg_accuracy': avg_accuracy,
                'avg_delay': avg_delay
            },
            'network_metrics': {
                'avg_throughput': avg_throughput,
                'avg_round_time': avg_round_time
            },
            'maddpg_metrics': {
                'avg_reward': avg_reward,
                'replay_buffer_size': maddpg_stats['replay_buffer_size']
            },
            'task_similarity_metrics': {
                'shared_task_groups': shared_task_count,
                'independent_tasks': independent_task_count
            }
        }

    def run_experiment(self):
        """Run the complete multi-task federated learning experiment"""
        self.logger.info("Starting multi-task federated learning experiment")

        # Setup - use our custom boot instead of parent's boot()
        self.setup_network_simulation()
        self.custom_boot()

    def custom_boot(self):
        """Custom boot method that skips the parent's data and model loading"""
        logging.info('Booting multi-task federated learning server...')

        # Create clients directly using our make_clients method
        total_clients = self.config.clients.total
        self.make_clients(total_clients)

        # Initialize model path (for compatibility)
        model_path = self.config.paths.model
        sys.path.append(model_path)

        # Run federated rounds
        for round_num in range(self.mt_config.num_rounds):
            self.current_round = round_num

            try:
                round_metrics = self.run_federated_round(round_num)

                # Log progress
                if round_num % 10 == 0:
                    self._log_progress(round_num, round_metrics)

            except Exception as e:
                self.logger.error(f"Round {round_num} failed: {e}")
                raise

        # Final results
        self._generate_final_report()

        # Cleanup
        if self.network_connected:
            self.network.close()

        self.logger.info("Multi-task federated learning experiment completed")

    def _log_progress(self, round_num: int, metrics: Dict[str, Any]):
        """Log training progress"""
        training = metrics['training_metrics']
        network = metrics['network_metrics']
        maddpg = metrics['maddpg_metrics']

        self.logger.info(f"Round {round_num} Progress:")
        self.logger.info(
            f"  Training - Loss: {training['avg_loss']:.4f}, Accuracy: {training['avg_accuracy']:.4f}, Delay: {training['avg_delay']:.2f}s")
        self.logger.info(
            f"  Network - Throughput: {network['avg_throughput']:.2f}, Round Time: {network['avg_round_time']:.2f}s")
        self.logger.info(
            f"  MADDPG - Avg Reward: {maddpg['avg_reward']:.3f}, Buffer Size: {maddpg['replay_buffer_size']}")

    def _generate_final_report(self):
        """Generate final experiment report"""
        if not self.round_metrics:
            self.logger.warning("No round metrics available for final report")
            return

        # Calculate final statistics
        final_loss = self.round_metrics[-1]['training_metrics']['avg_loss']
        initial_loss = self.round_metrics[0]['training_metrics']['avg_loss']
        loss_improvement = initial_loss - final_loss

        final_accuracy = self.round_metrics[-1]['training_metrics']['avg_accuracy']
        initial_accuracy = self.round_metrics[0]['training_metrics']['avg_accuracy']
        accuracy_improvement = final_accuracy - initial_accuracy

        avg_throughput = np.mean(
            [rm['network_metrics']['avg_throughput'] for rm in self.round_metrics])
        avg_reward = np.mean([rm['maddpg_metrics']['avg_reward']
                             for rm in self.round_metrics])

        self.logger.info("="*60)
        self.logger.info("MULTI-TASK FEDERATED LEARNING FINAL REPORT")
        self.logger.info("="*60)
        self.logger.info(f"Total Rounds: {len(self.round_metrics)}")
        self.logger.info(
            f"Loss Improvement: {loss_improvement:.4f} ({initial_loss:.4f} → {final_loss:.4f})")
        self.logger.info(
            f"Accuracy Improvement: {accuracy_improvement:.4f} ({initial_accuracy:.4f} → {final_accuracy:.4f})")
        self.logger.info(f"Average Network Throughput: {avg_throughput:.2f}")
        self.logger.info(f"Average MADDPG Reward: {avg_reward:.3f}")
        self.logger.info(
            f"MADDPG Buffer Size: {self.maddpg_allocator.get_training_stats()['replay_buffer_size']}")
        self.logger.info("="*60)
